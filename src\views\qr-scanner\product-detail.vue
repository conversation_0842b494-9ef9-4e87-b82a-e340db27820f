<template>
  <!-- 全局 Toast 提示 -->
  <div v-if="toast.show" class="fixed top-[1.5vh] left-1/2 -translate-x-1/2 z-50 w-auto max-w-sm rounded-2xl bg-gray-900/95 dark:bg-gray-50/95 text-white dark:text-black shadow-2xl backdrop-blur-sm p-[1.25vh] animate-fade-in-down border border-gray-700/20 dark:border-gray-200/20">
    <p class="font-bold text-[1rem]">{{ toast.title }}</p>
    <p class="text-[0.875rem] opacity-90 mt-[0.25vh]">{{ toast.description }}</p>
  </div>

  <!-- 确认对话框 -->
  <div v-if="showConfirmModal" class="fixed inset-0 z-50 flex items-center justify-center p-[1.5vh]">
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" @click="showConfirmModal = false"></div>

    <!-- 对话框内容 -->
    <div class="relative bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 max-w-sm w-full mx-auto animate-fade-in-up">
      <!-- 头部 -->
      <div class="p-[1.5vh] border-b border-gray-200/50 dark:border-gray-700/50">
        <div class="flex items-center gap-[0.75vw]">
          <div class="w-[2.5rem] h-[2.5rem] rounded-xl bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
            <svg v-if="operationType === 'outbound'" class="w-[1.25rem] h-[1.25rem] text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <svg v-else class="w-[1.25rem] h-[1.25rem] text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18m-6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
          </div>
          <h3 class="text-[1.125rem] font-bold text-gray-900 dark:text-white">确认{{ operationType === 'outbound' ? '出库' : '入库' }}</h3>
        </div>
      </div>

      <!-- 内容 -->
      <div class="p-[1.5vh] space-y-[1vh]">
        <div class="rounded-xl bg-gray-50 dark:bg-gray-800/50 p-[1vh]">
          <p class="text-[0.875rem] text-gray-600 dark:text-gray-400 mb-[0.5vh]">商品信息</p>
          <p class="font-semibold text-gray-900 dark:text-white">{{ product.name }}</p>
          <p class="text-[0.75rem] text-gray-500 dark:text-gray-500">编码: {{ product.productCode }}</p>
        </div>

        <div class="rounded-xl bg-blue-50 dark:bg-blue-950/30 p-[1vh]">
          <p class="text-[0.875rem] text-blue-600 dark:text-blue-400 mb-[0.5vh]">{{ operationType === 'outbound' ? '出库' : '入库' }}数量</p>
          <p class="font-bold text-blue-900 dark:text-blue-100">
            {{ plannedQuantities.map((qty, index) => `${qty || 0}${product.unitNames[index] || (index === 0 ? '件' : '包')}`).filter((_, index) => Number(plannedQuantities[index]) > 0).join(' + ') }}
          </p>
        </div>

        <div class="text-center">
          <p class="text-[0.875rem] text-gray-600 dark:text-gray-400">
            {{ operationType === 'outbound' ? '📤' : '📥' }}
            确认要执行此{{ operationType === 'outbound' ? '出库' : '入库' }}操作吗？
          </p>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="p-[1.5vh] border-t border-gray-200/50 dark:border-gray-700/50">
        <div class="grid grid-cols-2 gap-[1vw]">
          <button @click="showConfirmModal = false" class="h-[2.75rem] rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold transition-all duration-200">
            取消
          </button>
          <button @click="handleConfirm" :disabled="isSubmitting" class="h-[2.75rem] rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold transition-all duration-200 flex items-center justify-center gap-[0.5vw]">
            <Loader2 v-if="isSubmitting" class="h-[1rem] w-[1rem] animate-spin" />
            <template v-else>确认</template>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="w-full min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 font-sans">
    <div class="mx-auto max-w-md bg-white/80 dark:bg-black/80 backdrop-blur-sm">

      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center p-[3vh] space-y-[1vh]">
        <div class="relative">
          <div class="w-[4rem] h-[4rem] rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse"></div>
          <Loader2 class="absolute inset-0 h-[4rem] w-[4rem] animate-spin text-white" />
        </div>
        <span class="text-gray-600 dark:text-gray-400 font-medium">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="flex items-center justify-center min-h-screen">
        <div class="w-full max-w-sm mx-auto rounded-2xl bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50 p-[2vh] text-center shadow-lg border border-red-200/50 dark:border-red-800/50">
          <div class="w-[4rem] h-[4rem] mx-auto mb-[1.5vh] rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
            <svg class="w-[2rem] h-[2rem] text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <p class="text-red-700 dark:text-red-300 font-medium mb-[2vh] text-[1rem] leading-relaxed">{{ error }}</p>
          <div class="flex justify-center">
            <button @click="loadProductData" class="px-[2vw] py-[1vh] bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105 min-h-[2.5rem] flex items-center justify-center">
              重新加载
            </button>
          </div>
        </div>
      </div>

      <!-- 主体内容 -->
      <main v-else class="p-[1.5vh] pt-[2vh] pb-[12vh]">
        <div class="space-y-[1.5vh]">
          <!-- 商品信息卡片 -->
          <div class="group rounded-2xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900/90 dark:to-gray-800/50 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm">
            <div class="flex items-center gap-[1.25vw] p-[1.5vh]">
              <div class="relative">
                <div class="h-[6rem] w-[6rem] rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 overflow-hidden shadow-lg border border-gray-200/50 dark:border-gray-600/50">
                  <imagePreview :imgurl="product.image" :width="96" :height="96" :type="1" />
                </div>
                <div class="absolute -bottom-[0.5vh] -right-[0.5vw] w-[1.5rem] h-[1.5rem] bg-green-500 rounded-full border-2 border-white dark:border-gray-900 shadow-sm"></div>
              </div>
              <div class="flex-1 space-y-[0.5vh]">
                <h2 class="text-[1.25rem] font-bold text-gray-900 dark:text-white leading-tight">{{ product.name }}</h2>
                <span class="inline-flex items-center px-[0.625vw] py-[0.25vh] rounded-lg text-[0.75rem] font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">
                    ID: {{ product.id }}
                </span>
                <p class="text-[0.875rem] text-gray-600 dark:text-gray-400 font-medium">
                  📦 {{ product.unitGroupStr || '暂无库存信息' }}
                </p>
              </div>
            </div>
          </div>

          <!-- 产品编码卡片 -->
          <div class="rounded-2xl bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/50 dark:to-blue-950/50 p-[1.25vh] shadow-lg border border-indigo-200/50 dark:border-indigo-800/50">
            <div class="flex items-center gap-[0.75vw] mb-[0.5vh]">
              <div class="w-[2rem] h-[2rem] rounded-lg bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center">
                <svg class="w-[1rem] h-[1rem] text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                </svg>
              </div>
              <p class="text-[0.875rem] font-semibold text-indigo-700 dark:text-indigo-300">产品编码</p>
            </div>
            <p class="font-mono text-[1.125rem] font-bold text-indigo-900 dark:text-indigo-100 bg-white/50 dark:bg-black/20 rounded-lg px-[0.75vw] py-[0.5vh] border border-indigo-200/50 dark:border-indigo-700/50">{{ product.productCode }}</p>
          </div>

          <!-- 操作区域卡片 -->
          <div class="rounded-2xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900/90 dark:to-gray-800/50 shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden backdrop-blur-sm">
            <!-- 操作类型选择头部 -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50 p-[1.5vh] border-b border-gray-200/50 dark:border-gray-600/50">
              <div class="flex items-center gap-[0.75vw] mb-[1vh]">
                <div class="w-[2rem] h-[2rem] rounded-lg bg-emerald-100 dark:bg-emerald-900/50 flex items-center justify-center">
                  <svg class="w-[1rem] h-[1rem] text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                  </svg>
                </div>
                <h3 class="text-[1.125rem] font-bold text-gray-900 dark:text-white">库存操作</h3>
              </div>
              <!-- 出入库切换 - 分段控制器 -->
              <el-segmented
                v-model="operationType"
                :options="operationOptions"
                size="large"
                @change="handleOperationTypeChange"
                :class="[
                  'w-full'
                ]"
              />
            </div>

            <!-- 数量输入区域 -->
            <div class="p-[1.5vh] space-y-[1.5vh]">
              <!-- 动态渲染数量输入 -->
              <div v-for="(unitName, index) in product.unitNames" :key="index" class="space-y-[0.75vh]">
                <div class="flex items-center gap-[0.5vw]">
                  <div class="w-[1.5rem] h-[1.5rem] rounded-md bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                    <span class="text-[0.75rem] font-bold text-blue-600 dark:text-blue-400">{{ unitName?.charAt(0) || (index === 0 ? '件' : '包') }}</span>
                  </div>
                  <label class="font-semibold text-gray-800 dark:text-gray-200">计划数量 ({{ unitName || (index === 0 ? '件' : '包') }})</label>
                </div>
                <div class="flex items-center gap-[1vw]">
                  <!-- 减少按钮 -->
                  <div
                    @click="Number(plannedQuantities[index]) > 0 && handleClick(-1, index)"
                    :class="[
                      'group relative h-[3.5rem] w-[3.5rem] shrink-0 rounded-2xl transition-all duration-300 transform select-none overflow-hidden',
                      Number(plannedQuantities[index]) <= 0
                        ? 'cursor-not-allowed opacity-40'
                        : 'cursor-pointer hover:scale-105 active:scale-95'
                    ]"
                  >
                    <!-- 边框装饰 -->
                    <div :class="[
                      'absolute inset-0 rounded-2xl border-2 transition-colors duration-300',
                      Number(plannedQuantities[index]) <= 0
                        ? 'border-gray-300 dark:border-gray-600'
                        : 'border-blue-200 dark:border-blue-700 group-hover:border-blue-400 dark:group-hover:border-blue-500'
                    ]"></div>

                    <!-- 按压波纹效果 -->
                    <div class="absolute inset-0 rounded-2xl bg-blue-400/20 scale-0 group-active:scale-100 transition-transform duration-200"></div>

                    <!-- 图标容器 -->
                    <div class="relative z-10 flex items-center justify-center h-full">
                      <Minus :class="[
                        'h-6 w-6 transition-colors duration-300',
                        Number(plannedQuantities[index]) <= 0
                          ? 'text-gray-400 dark:text-gray-600'
                          : 'text-blue-500 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-300'
                      ]" />
                    </div>
                  </div>

                  <!-- 输入框 -->
                  <div class="flex-1 relative">
                    <el-input
                      v-model="plannedQuantities[index]"
                      type="tel"
                      placeholder="0"
                      :validate-event="false"
                      :class="[
                        'quantity-input',
                        errors[`unit_${index}`] ? 'error-state' : ''
                      ]"
                      @input="handleInputChange(index)"
                    />
                  </div>

                  <!-- 增加按钮 -->
                  <div
                    @click="!(operationType === 'outbound' && Number(plannedQuantities[index]) >= product.stock[index]) && handleClick(1, index)"
                    :class="[
                      'group relative h-[3.5rem] w-[3.5rem] shrink-0 rounded-2xl transition-all duration-300 transform select-none overflow-hidden',
                      (operationType === 'outbound' && Number(plannedQuantities[index]) >= product.stock[index])
                        ? 'cursor-not-allowed opacity-40'
                        : 'cursor-pointer hover:scale-105 active:scale-95'
                    ]"
                  >
                    <!-- 边框装饰 -->
                    <div :class="[
                      'absolute inset-0 rounded-2xl border-2 transition-colors duration-300',
                      (operationType === 'outbound' && Number(plannedQuantities[index]) >= product.stock[index])
                        ? 'border-gray-300 dark:border-gray-600'
                        : 'border-blue-200 dark:border-blue-700 group-hover:border-blue-400 dark:group-hover:border-blue-500'
                    ]"></div>

                    <!-- 按压波纹效果 -->
                    <div class="absolute inset-0 rounded-2xl bg-blue-400/20 scale-0 group-active:scale-100 transition-transform duration-200"></div>

                    <!-- 图标容器 -->
                    <div class="relative z-10 flex items-center justify-center h-full">
                      <Plus :class="[
                        'h-6 w-6 transition-colors duration-300',
                        (operationType === 'outbound' && Number(plannedQuantities[index]) >= product.stock[index])
                          ? 'text-gray-400 dark:text-gray-600'
                          : 'text-blue-500 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-300'
                      ]" />
                    </div>
                  </div>
                </div>
                <p v-if="errors[`unit_${index}`]" class="text-[0.875rem] text-red-600 dark:text-red-400 font-medium bg-red-50 dark:bg-red-950/50 rounded-lg px-[0.75vw] py-[0.5vh] border border-red-200 dark:border-red-800">{{ errors[`unit_${index}`] }}</p>
              </div>


            </div>
          </div>
        </div>
      </main>

      <!-- 底部操作栏 -->
      <footer class="fixed bottom-0 left-0 right-0 z-20">
        <!-- 渐变背景遮罩 -->
        <div class="absolute inset-x-0 -top-[2vh] h-[2vh] bg-gradient-to-t from-white/90 to-transparent dark:from-black/90 pointer-events-none"></div>

        <div class="relative bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-700/50 p-[1.5vh] shadow-2xl max-w-md mx-auto">
          <!-- 搜索按钮 - 如果是从搜索页面来的，显示搜索按钮 -->
          <div v-if="route.query.from !== 'search'" class="mb-[1vh]">
            <button @click="goToSearch" class="w-full h-[2.75rem] rounded-xl bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-semibold transition-all duration-200 flex items-center justify-center gap-[0.5vw] transform hover:scale-[1.02] active:scale-[0.98]">
              <svg class="w-[1rem] h-[1rem]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              搜索其他产品
            </button>
          </div>

          <div class="grid grid-cols-2 gap-[1vw]">
            <!-- 返回按钮 -->
            <button @click="goBack" class="group h-[3.5rem] rounded-2xl bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-700 dark:hover:to-gray-600 text-gray-800 dark:text-gray-200 font-bold transition-all duration-200 transform active:scale-95 shadow-lg border border-gray-300/50 dark:border-gray-600/50">
              <span class="flex items-center justify-center gap-[0.5vw]">
                <svg class="w-[1.25rem] h-[1.25rem] transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                返回
              </span>
            </button>

            <!-- 确认按钮 -->
            <button @click="showConfirmDialog" :disabled="!canSubmit" :class="['group h-[3.5rem] rounded-2xl font-bold flex items-center justify-center transition-all duration-200 transform shadow-lg border', !canSubmit ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-400/50 dark:border-gray-600/50' : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white active:scale-95 border-blue-600/50']">
              <span class="flex items-center justify-center gap-[0.5vw]">
                <Loader2 v-if="isSubmitting" class="h-[1.25rem] w-[1.25rem] animate-spin" />
                <template v-else>
                  <svg v-if="operationType === 'outbound'" class="w-[1.25rem] h-[1.25rem] transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  <svg v-else class="w-[1.25rem] h-[1.25rem] transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18m-6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                </template>
                {{ confirmButtonText }}
              </span>
            </button>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Minus, Plus, Loader2 } from 'lucide-vue-next'
import { ElSegmented, ElInput } from 'element-plus'
import { getProductByCode, getProductSkuById, getSkuList, changeStock } from '@/api/product'
import imagePreview from '@/components/imagePreview/index.vue'

const router = useRouter()
const route = useRoute()

// --- 状态定义 ---
const loading = ref(true)
const error = ref('')
const product = reactive({
  id: 0,
  productId: 0,
  name: "",
  image: "",
  productCode: "",
  unitGroupStr: "",
  stock: [] as number[],
  unitNames: [] as string[],
})

const plannedQuantities = ref<string[]>([])
const operationType = ref('outbound') // 'outbound' | 'inbound'
const isSubmitting = ref(false)
const errors = reactive<{ [key: string]: string }>({})
const showConfirmModal = ref(false)

// 分段控制器选项
const operationOptions = [
  {
    label: '📤 出库',
    value: 'outbound'
  },
  {
    label: '📥 入库',
    value: 'inbound'
  }
]

// --- Toast 提示状态 ---
const toast = reactive({
  show: false,
  title: '',
  description: ''
})

// --- 计算属性 ---
const confirmButtonText = computed(() => operationType.value === 'outbound' ? '确认出库' : '确认入库')
const hasError = computed(() => Object.keys(errors).length > 0)
const hasAnyQuantity = computed(() => plannedQuantities.value.some(qty => Number(qty) > 0))
const canSubmit = computed(() => {
  return !isSubmitting.value && !hasError.value && hasAnyQuantity.value
})

// --- 监听器 ---
watch([plannedQuantities, operationType], () => {
  const newErrors: { [key: string]: string } = {}

  // 验证数量
  if (operationType.value === 'outbound') {
    plannedQuantities.value.forEach((qty, index) => {
      const stockValue = product.stock[index] || 0
      if (Number(qty) > stockValue) {
        const unitName = product.unitNames[index] || (index === 0 ? '件' : '包')
        newErrors[`unit_${index}`] = `最多可出库 ${stockValue} ${unitName}`
      }
    })
  }

  // 清除所有错误
  Object.keys(errors).forEach(key => delete errors[key])
  // 设置新错误
  Object.assign(errors, newErrors)
}, { deep: true })

// --- 方法 ---
function handleOperationTypeChange(value: 'outbound' | 'inbound') {
  operationType.value = value
  plannedQuantities.value = product.unitNames.map(() => '')
}

function showToastNotification(title: string, description: string) {
  toast.title = title
  toast.description = description
  toast.show = true
  setTimeout(() => {
    toast.show = false
  }, 3000)
}

function showConfirmDialog() {
  if (!canSubmit.value) return
  showConfirmModal.value = true
}

async function handleConfirm() {
  if (!canSubmit.value) return

  // 关闭确认对话框
  showConfirmModal.value = false
  isSubmitting.value = true

  try {
    // 调用出入库API
    const type = operationType.value === 'outbound' ? 2 : 1 // 1=入库，2=出库

    // 构建数量数组
    const nums = plannedQuantities.value.map(qty => Number(qty) || 0)

    const requestData = {
      type: type,
      nums: nums,
      skuId: product.id,
      productId: product.productId
    }

    console.log('调用changeStock，参数：', requestData)
    await changeStock(requestData)

    const quantityText = plannedQuantities.value
      .map((qty, index) => `${qty || 0}${product.unitNames[index] || (index === 0 ? '件' : '包')}`)
      .filter((_, index) => Number(plannedQuantities.value[index]) > 0)
      .join(', ')

    showToastNotification(
      `${operationType.value === 'outbound' ? '出库' : '入库'}成功!`,
      `${product.name}: ${quantityText}`
    )

    // 重新加载商品数据以更新库存
    setTimeout(() => {
      loadProductData()
      plannedQuantities.value = product.unitNames.map(() => '')
    }, 1000)
  } catch (err: any) {
    showToastNotification('操作失败', err.message || '请稍后重试')
  } finally {
    isSubmitting.value = false
  }
}

// 返回上一页
function goBack() {
  router.back()
}

// 跳转到产品搜索页面
function goToSearch() {
  router.push('/product-search')
}

// 加载商品数据
async function loadProductData() {
  const productCode = route.query.id || route.params.id
  const fromSource = route.query.from // 获取来源标识

  if (!productCode) {
    error.value = '缺少商品编码参数'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''

    let response: any

    // 根据来源选择不同的API接口
    if (fromSource === 'search') {
      // 搜索来源使用 productInfo 接口
      response = await getProductSkuById(productCode as string)
    } else {
      // 扫码来源或其他情况使用 getProductByCode 接口
      response = await getProductByCode(productCode as string)
    }

    // 检查响应是否有效
    if (!response || (!response.id && !response.productName)) {
      throw new Error('商品不存在或已被删除')
    }

    const productData = response

    // 根据API响应结构更新商品信息
    Object.assign(product, {
      id: productData.id, // SKU ID
      productId: productData.productId, // 产品ID
      name: productData.productName || '未知商品',
      image: productData.image || '',
      productCode: productData.attrValueNo || productData.barCode || 'N/A',
      unitGroupStr: productData.unitGroupStr || '',
      unitNames: productData.unitNames || ['件', '包'],
      stock: productData.unitNums || [0, 0]
    })

    // 初始化计划数量数组
    plannedQuantities.value = product.unitNames.map(() => '')
  } catch (err: any) {
    console.error('加载商品信息失败:', err)
    error.value = err.message || '加载商品信息失败，请检查商品编码是否正确'
  } finally {
    loading.value = false
  }
}

// --- 数量选择器交互逻辑 ---
function updateValue(delta: number, index: number) {
  const max = operationType.value === 'outbound' ? product.stock[index] : undefined

  const currentValue = Number(plannedQuantities.value[index]) || 0
  let newValue = Math.max(0, currentValue + delta)
  if (max !== undefined && newValue > max) {
    newValue = max
  }
  plannedQuantities.value[index] = String(newValue)
}

function handleClick(delta: number, index: number) {
  updateValue(delta, index)
}

function handleInputChange(index: number) {
  // 清除该字段的错误状态
  delete (errors as any)[`unit_${index}`]

  // 验证输入值
  const value = Number(plannedQuantities.value[index])
  if (isNaN(value) || value < 0) {
    plannedQuantities.value[index] = '0'
  }
}

onMounted(() => {
  loadProductData()
})
</script>

<style scoped>
/* 自定义动画 */
@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-10px) translateX(-50%);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

/* 卡片悬浮效果 */
.group:hover .group-hover\:shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 输入框聚焦效果 */
input:focus {
  transform: scale(1.02);
}

/* 按钮点击效果 */
.active\:scale-95:active {
  transform: scale(0.95);
}

.active\:scale-90:active {
  transform: scale(0.90);
}

/* 毛玻璃效果增强 */
.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色模式滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* 分段控制器自定义样式 */
:deep(.el-segmented) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  padding: 0.25rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

:deep(.el-segmented__item) {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
	color: #000;
	height: 3.125rem;
}

/* 深色模式分段控制器 */
.dark :deep(.el-segmented) {
  background: rgba(31, 41, 55, 0.8);
  border-color: rgba(75, 85, 99, 0.5);
}

.dark :deep(.el-segmented__item) {
  color: rgb(156, 163, 175);
}

.dark :deep(.el-segmented__item:hover) {
  color: rgb(209, 213, 219);
}

/* Element Plus 输入框自定义样式 */
:deep(.quantity-input .el-input__wrapper) {
  height: 3rem;
  border-radius: 1rem;
  border: 2px solid #e5e7eb;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.quantity-input .el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

:deep(.quantity-input .el-input__wrapper.is-focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

:deep(.quantity-input .el-input__inner) {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  color: #1f2937;
}

/* 错误状态样式 */
:deep(.error-state .el-input__wrapper) {
  border-color: #ef4444;
}

:deep(.error-state .el-input__wrapper:hover) {
  border-color: #dc2626;
}

:deep(.error-state .el-input__wrapper.is-focus) {
  border-color: #dc2626;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* 暗色模式 */
.dark :deep(.quantity-input .el-input__wrapper) {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-color: #4b5563;
}

.dark :deep(.quantity-input .el-input__inner) {
  color: #f9fafb;
}

.dark :deep(.quantity-input .el-input__wrapper:hover) {
  border-color: #60a5fa;
}

.dark :deep(.quantity-input .el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.2);
}
</style>
