<template>
  <div>
    <el-row v-if="type == 2"><div class="info-title">基础信息</div></el-row>
    <el-row v-if="type == 2">
      <el-form
        :model="form"
        ref="ruleFormRef"
        :rules="rules"
        :inline="false"
        class="sku-form"
        :label-width="120"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号码"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                v-model="form.customerName"
                placeholder="请输入客户名称"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="审批" prop="examineApprove">
              <el-input
                v-model="form.examineApprove"
                placeholder="请输入审批人"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核" prop="examine">
              <el-input
                v-model="form.examine"
                placeholder="请输入审核人"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="制单" prop="documentPreparation">
              <el-input
                v-model="form.documentPreparation"
                placeholder="请输入制单人"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货人" prop="consignee">
              <el-input
                v-model="form.consignee"
                placeholder="请输入收货人"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="货车司机" prop="driver">
              <el-input
                v-model="form.driver"
                placeholder="请输入货车司机"
                size="default"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 空列，保持布局平衡 -->
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="mark">
              <el-input
                type="textarea"
                v-model="form.mark"
                placeholder="填写备注"
                size="large"
                :rows="3"
                class="w-full"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
    <el-row>
      <div class="info-title">{{ stepTitle }}</div>
      <span class="info-tip">（可右键操作）</span>
    </el-row>
    <el-row>
      <Table
        ref="table"
        :data="form.outboundEntityList"
        :showPage="false"
        class="w-full sku-table"
        :showContextMenu="true"
        :contextMenuItems="contextMenuItems"
        @context-menu-click="handleContextMenuClick"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column label="商品图" align="center">
          <template #default="scope">
            <imagePreview :imgurl="scope.row.image"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column prop="attrValueNo" label="产品编码" align="center">
        </el-table-column>
        <el-table-column prop="suk" label="商品规格" align="center" />
        <el-table-column prop="unitGroupStr" label="库存" align="center" />
				        <el-table-column :label="stepSkuTitle" align="center">
          <template #default="scope">
            <div v-for="(item, index) in scope.row.unitNames" class="unit1">
              <div>{{ item }}:</div>
              <el-input-number
                class="myinput"
                :model-value="getUnitValue(scope.row, index)"
                @input="value => updateUnitNum(scope.row, index, value)"
                placeholder="请输入数量"
                :min="0"
                controls-position="right"
              ></el-input-number>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="单价" align="center" width="140">
          <template #default="scope">
            <el-input-number
              class="price-input"
              :model-value="scope.row.unitPrice || 0"
              @input="value => updateUnitPrice(scope.row, value)"
              placeholder="请输入单价"
              :min="0"
              :precision="2"
              :step="0.01"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="金额" align="center" width="140">
          <template #default="scope">
            <el-input-number
              class="price-input"
              :model-value="scope.row.price || 0"
              @input="value => updatePrice(scope.row, value)"
              placeholder="请输入金额"
              :min="0"
              :precision="2"
              :step="0.01"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" width="200">
          <template #default="scope">
            <el-input
              :model-value="scope.row.logMark || ''"
              @input="value => updateLogMark(scope.row, value)"
              placeholder="请输入备注"
              size="default"
              maxlength="100"
              show-word-limit
            ></el-input>
          </template>
        </el-table-column>
      </Table>
    </el-row>

		    <!-- 总金额展示区域 - 移动到表格上方 -->
    <el-row v-if="form.outboundEntityList && form.outboundEntityList.length > 0" class="total-amount-row">
      <el-col :span="24">
        <div class="total-amount-container">
          <div class="total-amount-card product-summary">
            <div class="total-amount-item">
              <span class="total-amount-label">商品总金额：</span>
              <span class="total-amount-value product-total">¥{{ formatPrice(totalAmount) }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 选择赠品按钮 -->
    <el-row v-if="type == '2'" class="gift-button-row">
      <el-button type="primary" @click="handleOpenGiftDialog">选择赠品</el-button>
    </el-row>

    <!-- 赠品货品明细表格 -->
    <el-row class="gift-title-row" v-if="type == '2' && form.giftList.length > 0">
      <div class="info-title">赠品货品明细</div>
      <span class="info-tip">（可右键操作）</span>
    </el-row>
    <el-row v-if="type == '2' && form.giftList.length > 0">
      <Table
        ref="giftTable"
        :data="form.giftList"
        :showPage="false"
        class="w-full sku-table"
        :showContextMenu="true"
        :contextMenuItems="giftContextMenuItems"
        @context-menu-click="handleGiftContextMenuClick"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column label="商品图" align="center">
          <template #default="scope">
            <imagePreview :imgurl="scope.row.image"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column prop="attrValueNo" label="产品编码" align="center">
        </el-table-column>
        <el-table-column prop="suk" label="商品规格" align="center" />
        <el-table-column prop="unitGroupStr" label="库存" align="center" />
        <el-table-column label="计划出库数" align="center">
          <template #default="scope">
            <div v-for="(item, index) in scope.row.unitNames" class="unit1">
              <div>{{ item }}:</div>
              <el-input-number
                class="myinput"
                :model-value="getUnitValue(scope.row, index)"
                @input="(value: number) => updateUnitNum(scope.row, index, value)"
                placeholder="请输入数量"
                :min="0"
                controls-position="right"
              ></el-input-number>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="单价" align="center" width="140">
          <template #default="scope">
            <span class="gift-price-text">¥0.00（赠品）</span>
          </template>
        </el-table-column>
        <el-table-column label="金额" align="center" width="140">
          <template #default="scope">
            <span class="gift-price-text">¥0.00（赠品）</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" width="200">
          <template #default="scope">
            <el-input
              :model-value="scope.row.logMark || ''"
              @input="value => updateLogMark(scope.row, value)"
              placeholder="请输入备注"
              size="default"
              maxlength="100"
              show-word-limit
            ></el-input>
          </template>
        </el-table-column>
      </Table>
    </el-row>

    <!-- 赠品选择弹窗 -->
    <GiftSelectDialog
      v-model:visible="showGiftDialog"
      :selectedItems="form.giftList"
      @confirm="handleGiftConfirm"
      @cancel="handleGiftCancel"
    />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, watch } from 'vue'
  import {
    FormRules,
    FormInstance,
    ElMessage,
    ElMessageBox,
  } from 'element-plus'
  import { Delete } from '@element-plus/icons-vue'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import GiftSelectDialog from '@/views/main/product/orderOutbound/giftSelectDialog.vue'
  import { AddPurchaseOrderVo } from '@/type/order.type'
  import { addPurchaseOrder } from '@/api/order'
  const ruleFormRef = ref<FormInstance>()
  const showGiftDialog = ref(false) // 控制赠品选择弹窗显示
  const emit = defineEmits(['addActiveIndex', 'removeItem'])
  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: () => '1',
    },
  })

  // 右键菜单配置
  const contextMenuItems = ref([
    {
      key: 'delete',
      label: '删除',
      icon: Delete,
    },
  ])

  // 赠品右键菜单配置
  const giftContextMenuItems = ref([
    {
      key: 'delete',
      label: '删除',
      icon: Delete,
    },
  ])

  // 处理右键菜单点击
  const handleContextMenuClick = async ({
    item,
    row,
  }: {
    item: any
    row: any
  }) => {
    if (item.key === 'delete') {
      try {
        const productName = row.productName || '未知商品'
        await ElMessageBox.confirm(
          `确定要删除商品"${productName}"吗？`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // 从表格数据中移除
        const index = form.outboundEntityList.findIndex(
          item => item.id === row.id
        )
        if (index > -1) {
          form.outboundEntityList.splice(index, 1)
        }

        // 通知父组件移除该项
        emit('removeItem', row)

        ElMessage.success(`商品"${productName}"删除成功`)
      } catch {
        // 用户取消删除
      }
    }
  }

  // 处理赠品表格右键菜单点击
  const handleGiftContextMenuClick = async ({
    item,
    row,
  }: {
    item: any
    row: any
  }) => {
    if (item.key === 'delete') {
      try {
        const productName = row.productName || '未知商品'
        await ElMessageBox.confirm(
          `确定要删除赠品"${productName}"吗？`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        // 从赠品列表中移除
        const index = form.giftList.findIndex(
          item => item.id === row.id
        )
        if (index > -1) {
          form.giftList.splice(index, 1)
        }

        ElMessage.success(`赠品"${productName}"删除成功`)
      } catch {
        // 用户取消删除
      }
    }
  }

  // 处理赠品选择确认
  const handleGiftConfirm = (selectedItems: any[]) => {
    // 智能合并赠品数据，保留用户已填写的数量信息
    const mergedGiftList = selectedItems.map(newItem => {
      // 查找是否已存在相同的赠品
      const existingGift = form.giftList.find(existing => existing.id === newItem.id)
      
      if (existingGift) {
        // 如果已存在，保留用户填写的数量信息，但更新其他可能变化的字段
        return {
          ...newItem, // 使用新的基础数据
          unitNums: existingGift.unitNums || newItem.unitNums, // 保留已填写的数量
          changeUnitNums: existingGift.changeUnitNums || newItem.changeUnitNums, // 保留已填写的变更数量
          unitPrice: 0, // 赠品单价始终为0
          price: 0, // 赠品金额始终为0
          logMark: existingGift.logMark || '', // 保留已填写的备注
        }
      } else {
        // 如果是新赠品，使用默认数据
        return {
          ...newItem,
          unitPrice: 0, // 赠品单价为0
          price: 0, // 赠品金额为0
          logMark: '', // 新赠品备注为空
        }
      }
    })
    
    form.giftList = mergedGiftList
    showGiftDialog.value = false
    ElMessage.success(`已选择 ${selectedItems.length} 个赠品`)
  }

  // 处理赠品选择取消
  const handleGiftCancel = () => {
    showGiftDialog.value = false
  }

  // 处理打开赠品选择对话框
  const handleOpenGiftDialog = () => {
    // 确保传递给 GiftSelectDialog 的数据格式正确
    // 在打开对话框前，不需要特殊处理，因为 GiftSelectDialog 会通过 props.selectedItems 接收 form.giftList
    // 并且在组件内部会正确处理数据格式转换和回显
    showGiftDialog.value = true
  }
  const form = reactive({
    phone: '',
    mark: '',
    outboundEntityList: [] as any[],
    giftList: [] as any[], // 赠品列表
    // 新增的非必填字段
    customerName: '',
    examineApprove: '',
    examine: '',
    documentPreparation: '',
    consignee: '',
    driver: '',
  })
  const rules = reactive<FormRules>({
    phone: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  })
  const query = reactive<AddPurchaseOrderVo & { giftList: any[] }>({
    phone: '',
    mark: '',
    type: props.type,
    outboundEntityList: [],
    giftList: [], // 赠品列表
    // 新增的非必填字段
    customerName: '',
    examineApprove: '',
    examine: '',
    documentPreparation: '',
    consignee: '',
    driver: '',
  })

  /* 出库提交 */
  const onSubmit = async () => {
    if (!ruleFormRef.value) return
    ruleFormRef.value.validate(async (valid, fields) => {
      if (valid) {
        // 验证赠品数量（如果有赠品的话）
        if (form.giftList && form.giftList.length > 0) {
          const hasValidGiftQuantity = form.giftList.some((item: any) => {
            const nums = item.changeUnitNums || item.unitNums || []
            return nums.some((num: any) => num > 0)
          })

          if (!hasValidGiftQuantity) {
            ElMessage.error('请填写赠品的出库数量')
            return
          }
        }

        Object.assign(query, form)
        query.outboundEntityList = form.outboundEntityList.map(item => ({
          skuId: item.id,
          nums:
            item.changeUnitNums && Array.isArray(item.changeUnitNums)
              ? item.changeUnitNums
              : item.unitNums,
          unitPrice: item.unitPrice || 0, // 添加单价字段
          price: item.price || 0, // 添加金额字段
          logMark: item.logMark || '', // 添加备注字段
        }))
        // 处理赠品数据
        query.giftList = form.giftList.map(item => ({
          skuId: item.id,
          nums:
            item.changeUnitNums && Array.isArray(item.changeUnitNums)
              ? item.changeUnitNums
              : item.unitNums,
          unitPrice: 0, // 赠品单价为0
          price: 0, // 赠品金额为0
          logMark: item.logMark || '', // 添加备注字段
        }))
        try {
          await addPurchaseOrder(query)
          ElMessage.success('操作成功')
          // 清空赠品列表
          form.giftList = []
          emit('addActiveIndex')
        } catch (e) {
          console.log(e)
        }
      } else {
        ElMessage.warning('请将内容填写完整')
        console.log(fields)
      }
    })
  }

  /* 入库提交 */
  const onSubmit2 = async () => {
    // 验证赠品数量（如果有赠品的话）
    if (form.giftList && form.giftList.length > 0) {
      const hasValidGiftQuantity = form.giftList.some((item: any) => {
        const nums = item.changeUnitNums || item.unitNums || []
        return nums.some((num: any) => num > 0)
      })

      if (!hasValidGiftQuantity) {
        ElMessage.error('请填写赠品的出库数量')
        return
      }
    }

    Object.assign(query, form)
    query.outboundEntityList = form.outboundEntityList.map(item => ({
      skuId: item.id,
      nums:
        item.changeUnitNums && Array.isArray(item.changeUnitNums)
          ? item.changeUnitNums
          : item.unitNums,
      unitPrice: item.unitPrice || 0, // 添加单价字段
      price: item.price || 0, // 添加金额字段
      logMark: item.logMark || '', // 添加备注字段
    }))
    // 处理赠品数据
    query.giftList = form.giftList.map(item => ({
      skuId: item.id,
      nums:
        item.changeUnitNums && Array.isArray(item.changeUnitNums)
          ? item.changeUnitNums
          : item.unitNums,
      unitPrice: 0, // 赠品单价为0
      price: 0, // 赠品金额为0
      logMark: item.logMark || '', // 添加备注字段
    }))
    try {
      await addPurchaseOrder(query)
      ElMessage.success('操作成功')
      // 清空赠品列表
      form.giftList = []
      emit('addActiveIndex')
    } catch (e) {
      console.log(e)
    }
  }

  watch(
    () => props.list,
    newList => {
      // 智能合并数据，不覆盖用户填写的内容
      const existingItems = form.outboundEntityList || []
      const newItems = JSON.parse(JSON.stringify(newList))

      // 如果是第一次设置数据（没有现有数据）
      if (existingItems.length === 0) {
        form.outboundEntityList = newItems
      } else {
        // 已有数据，需要智能合并
        const mergedItems = []

        // 处理新数据中的每一项
        newItems.forEach(newItem => {
          // 查找是否已存在相同的项
          const existingItem = existingItems.find(
            existing => existing.id === newItem.id
          )

          if (existingItem) {
            // 如果已存在，保持用户填写的数据
            mergedItems.push(existingItem)
          } else {
            // 如果是新项，添加新数据
            mergedItems.push(newItem)
          }
        })

        form.outboundEntityList = mergedItems
      }
    },
    {
      deep: true,
      immediate: true,
    }
  )
  const stepTitle = computed(() => {
    return `${props.type === '1' ? '入' : '出'}库货品明细`
  })
  const stepSkuTitle = computed(() => {
    return `计划${props.type === '1' ? '入' : '出'}库数`
  })

  // 获取数量值的函数
  const getUnitValue = (row: any, index: number) => {
    // 优先使用 changeUnitNums
    if (
      row.changeUnitNums &&
      Array.isArray(row.changeUnitNums) &&
      row.changeUnitNums[index] !== undefined
    ) {
      return row.changeUnitNums[index]
    }
    // 回退到 unitNums
    if (
      row.unitNums &&
      Array.isArray(row.unitNums) &&
      row.unitNums[index] !== undefined
    ) {
      return row.unitNums[index]
    }
    return 0
  }

  // 更新数量的函数
  const updateUnitNum = (row: any, index: number, value: number) => {
    // 如果有 changeUnitNums 字段，优先更新它
    if (row.changeUnitNums && Array.isArray(row.changeUnitNums)) {
      row.changeUnitNums[index] = value
    } else {
      // 否则更新 unitNums
      if (!row.unitNums) {
        row.unitNums = []
      }
      row.unitNums[index] = value
    }
  }

  // 更新单价的函数
  const updateUnitPrice = (row: any, value: number | null) => {
    row.unitPrice = value || 0
  }

  // 更新金额的函数
  const updatePrice = (row: any, value: number | null) => {
    row.price = value || 0
  }

  // 更新备注的函数
  const updateLogMark = (row: any, value: string) => {
    row.logMark = value || ''
  }

  // 计算总金额
  const totalAmount = computed(() => {
    if (!form.outboundEntityList || form.outboundEntityList.length === 0) {
      return 0
    }
    return form.outboundEntityList.reduce((total, item) => {
      const price = parseFloat(item.price) || 0
      return total + price
    }, 0)
  })

  // 格式化价格显示
  const formatPrice = (price: number) => {
    return price.toFixed(2)
  }


  defineExpose({
    onSubmit,
    onSubmit2,
    form,
  })
</script>

<style scoped lang="scss">
  // 使用主题变量的样式定义
  .sku-form {
    margin: var(--system-spacing-xl);
    padding: 0 var(--system-spacing-md);
  }

  .info-title {
    border-left: var(--system-title-border-width) solid var(--system-title-border-color);
    padding-left: var(--system-spacing-sm);
    margin-bottom: var(--system-spacing-sm);
    color: var(--system-page-color);
    font-weight: 600;
  }

  .info-tip {
    font-size: 14px;
    margin-left: var(--system-spacing-sm);
    color: var(--system-page-tip-color);
  }

  .sku-table {
    margin-top: var(--system-spacing-sm);
  }

  .total-amount-row {
    margin-top: var(--system-spacing-xs);
  }

  .gift-button-row {
    margin-top: var(--system-spacing-lg);
  }

  .gift-title-row {
    margin-top: var(--system-spacing-xs);
  }

  .gift-price-text {
    color: var(--system-page-muted-color);
  }

  .unit1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--system-spacing-sm);
  }

  .myinput {
    width: 80px;
  }

  .price-input {
    width: 120px;
  }

  /* 右键菜单选中行高亮样式 */
  :deep(.context-menu-selected-row) {
    background-color: color-mix(in srgb, var(--system-primary-color) 10%, transparent) !important;
    border: 1px solid var(--system-primary-color);
  }

  :deep(.context-menu-selected-row:hover) {
    background-color: color-mix(in srgb, var(--system-primary-color) 10%, transparent) !important;
  }

  /* 总金额展示样式 */
  .total-amount-container {
    display: flex;
    justify-content: flex-start;
  }

  .total-amount-card {
    background: linear-gradient(135deg, var(--system-card-background) 0%, color-mix(in srgb, var(--system-page-border-color) 20%, var(--system-card-background)) 100%);
    border: 1px solid var(--system-card-border-color);
    border-radius: var(--system-card-radius);
    padding: var(--system-spacing-lg) var(--system-spacing-xl);
    box-shadow: var(--system-card-shadow);
    min-width: 280px;
  }

  .total-amount-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--system-spacing-sm);
  }

  .total-amount-item:last-child {
    margin-bottom: 0;
  }

  .total-amount-label {
    font-size: 14px;
    color: var(--system-page-secondary-color);
    font-weight: 500;
  }

  .total-amount-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--system-page-color);
  }

  .total-amount-item:first-child .total-amount-value {
    color: var(--system-page-danger-color);
    font-size: 18px;
  }

  .gift-info .total-amount-value {
    color: var(--system-page-success-color);
    font-size: 14px;
  }

  /* 商品总金额汇总样式 - 与赠品样式保持一致 */
  .product-summary {
    background: linear-gradient(135deg, color-mix(in srgb, var(--system-primary-color) 5%, var(--system-card-background)) 0%, color-mix(in srgb, var(--system-primary-color) 10%, var(--system-card-background)) 100%);
    border: 1px solid var(--system-primary-color);
  }

  .product-total {
    color: var(--system-primary-color) !important;
    font-size: 18px !important;
    font-weight: 700 !important;
  }
</style>
